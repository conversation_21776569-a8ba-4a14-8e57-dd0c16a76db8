# Step 1: Building the application with maven
# Etapa 1: Construindo a aplicação com maven
FROM maven:3.9-eclipse-temurin-21-alpine AS builder

WORKDIR /app

COPY . .

# Executa o build do projeto, gerando o arquivo .jar dentro da pasta target
RUN ./mvnw clean package -DskipTests

# Etapa 2: Runtime da aplicação
FROM eclipse-temurin:21-jdk-alpine

WORKDIR /app

# Estou pegando o arquivo jar de dentro de target, gerado no builder, o passando para um arquivo app.jar
COPY --from=builder /app/target/*.jar app.jar

EXPOSE 8080

ENTRYPOINT [ "java", "-jar", "app.jar" ]