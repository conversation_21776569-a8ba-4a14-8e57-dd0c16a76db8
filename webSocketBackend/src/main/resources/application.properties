spring.application.name=webSocketBackend

# Configurações para AWS S3
spring.cloud.aws.credentials.access-key=accesskey
spring.cloud.aws.credentials.secret-key=secretaccesskey
spring.cloud.aws.region.static=sa-east-1

# Endpoint do LocalStack (substitua pelo endpoint real caso esteja usando a AWS)
spring.cloud.aws.s3.endpoint=${AWS_ENDPOINT:http://localhost:4566}
spring.cloud.aws.s3.region=sa-east-1
spring.cloud.aws.s3.bucket-name=album-pictures

spring.cloud.aws.dynamodb.table-name=albums

spring.devtools.restart.exclude=dev/MSpilari/webSocketBackend/models/**

frontend.url = ${FRONTEND_URL:http://localhost:5173}